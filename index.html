<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>PseudoWrite</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    html, body { box-sizing: border-box; }
    *, *:before, *:after { box-sizing: inherit; }
    body { margin: 0; font-family: system-ui, sans-serif; background: #f6f6f6; color: #222; }
    .container { display: flex; height: 100vh; }
    .sidebar { width: 280px; background: #29293a; color: #eee; padding: 0; display: flex; flex-direction: column; will-change: opacity, transform; }
    .main { flex: 1; display: flex; flex-direction: column; }
    .header { padding: 1rem; background: #333354; color: #fff; font-size: 1.5rem; }
    .editor { flex: 1; padding: 1.25rem; background: #fff; border-left: 1px solid #ddd; min-height: 0; }
    .footer { padding: 0.6rem 1.2rem; background: #f0f0f4; border-top: 1px solid #ddd; color: #888; font-size: 0.95rem; }
    .modal { display: none; position: fixed; left: 0; top: 0; width: 100vw; height: 100vh; background: rgba(40,42,54,0.7); align-items: center; justify-content: center; z-index: 2000; will-change: opacity, transform; }
    .modal-content { background: #fafaff; padding: 2rem; border-radius: 6px; min-width: 380px; min-height: 200px; box-shadow: 0 4px 32px #0002;}
    /* Accessibility: Always show focus ring for keyboard users on controls */
    button:focus-visible, input:focus-visible, textarea:focus-visible, select:focus-visible {
      outline: 3px solid #335cda !important;
      outline-offset: 2px !important;
      box-shadow: 0 0 0 2px #d0e1fa, 0 0 0 5px #335cda55;
      z-index: 10;
    }
    button:focus, input:focus, textarea:focus, select:focus {
      outline: 2px solid #335cda;
      z-index: 10;
    }
    /* Ensure sidebar and modal quick-add/cancel/delete buttons get the same focus ring */
    .sidebar button:focus-visible, .modal-content button:focus-visible {
      outline: 3px solid #ffb338 !important;
      box-shadow: 0 0 0 2px #fbf3d0, 0 0 0 6px #ffb33840;
    }
  </style>
</head>
<body>
  <div id="debug-status" style="background:#fffee0;color:#000;font-family:monospace;font-size:1.1em;padding:4px 13px;position:fixed;z-index:99999;top:0;right:0;max-width:60vw;box-shadow:0 2px 22px #ffea;display:none"></div>
  <div class="container">
    <aside class="sidebar">
      <div class="header" style="position:relative;display:flex;align-items:center;">
        Story Bible
        <button id="show-help" title="Quick Guide" style="margin-left:auto; margin-right:0.45rem; background:#e4e4ff; color:#2a337a; border:none; border-radius:50%; width:1.6em; height:1.6em; font-weight:bold; font-size:1em; cursor:pointer; display:flex; align-items:center; justify-content:center;">?</button>
      </div>
      <!-- Onboarding/help banner -->
      <div id="onboard-banner" style="display:none; background:#f5f7ff; border-bottom:1px solid #c2c7e4; color:#234; padding:1.25em 1.5em 1.25em 2.25em; font-size:1.09em; line-height:1.6; position: relative;">
        <div style="position:absolute;left:1em;top:1.07em;font-weight:bold;font-size:1.16em;color:#425;">🛈</div>
        <b>PseudoWrite Guide:</b><br>
        - Create and organize your story’s characters and worldbuilding in the sidebar.<br>
        - Use the <b>+</b> buttons to quickly add new story elements.<br>
        - Click an item to edit full details, save, or delete.<br>
        - Write, Rewrite, or Describe your draft with AI—context is drawn from your Story Bible.<br>
        - <b>Export</b> your project as backup. All data stays private & local.<br>
        - <u>Tip:</u> Lost work? Try exporting ASAP or resetting local storage.<br>
        <button id="hide-onboard" style="position:absolute;top:0.7em;right:1.1em;background:#ffc9c7;border:none;border-radius:9px;padding:1px 9px 2px 9px;color:#a30;font-weight:bold;font-size:0.97em;cursor:pointer;">×</button>
      </div>
      <!-- Sidebar categories will be dynamically rendered here -->
      <nav id="sidebar-nav" aria-label="Story Bible Navigation"></nav>
    </aside>
    <main class="main">
      <div id="editor-toolbar" style="display: flex; gap: 0.6rem; align-items: center; padding: 0.85rem 1.22rem 0 1.22rem; background: #f8fafd;">
        <button id="btn-write" style="padding: 0.55rem 0.98rem; border-radius: 4px; border: none; background: #335cda; color: #fff; font-weight: bold; cursor: pointer;">Write</button>
        <button id="btn-rewrite" style="padding: 0.47rem 1.09rem; border-radius: 4px; border: none; background: #7382aa; color: #fff; font-weight: bold; cursor: pointer;">Rewrite</button>
        <button id="btn-describe" style="padding: 0.5rem 1.09rem; border-radius: 4px; border: none; background: #4c7c4c; color: #fff; font-weight: bold; cursor: pointer;">Describe</button>
        <span id="toolbar-status" style="margin-left: 2.1rem; color: #6c7392; font-size: 0.98rem;"></span>
        <div style="flex: 1"></div>
        <button id="btn-export" title="Export project to JSON" style="margin-left: 1rem; padding: 0.41rem 1.1rem; border-radius: 4px; border: none; background: #e0d65e; color: #222; font-weight: bold; cursor: pointer;">Export</button>
        <label id="import-label" for="file-import" title="Import a PseudoWrite JSON file" style="margin-left: 0.4rem; padding: 0.41rem 1.1rem; border-radius: 4px; background: #8ac6e6; color: #143453; font-weight: bold; cursor: pointer;">
          Import<input id="file-import" type="file" accept=".json,application/json" style="display:none">
        </label>
      </div>
      <section class="editor" id="main-editor" contenteditable="true" spellcheck="true" aria-label="Write your story here..." role="region"></section>
      <footer class="footer">PseudoWrite | v0.1 | No cloud. 100% local.</footer>
    </main>
  </div>
  <!-- Modal containers for detail editing -->
  <div class="modal" id="detail-modal" role="dialog" aria-modal="true" aria-labelledby="modal-title">
    <div class="modal-content" id="modal-content">
      <!-- Modal contents set dynamically -->
    </div>
  </div>

  <!-- Input modal for creating new items -->
  <div class="modal" id="input-modal" role="dialog" aria-modal="true" aria-labelledby="input-modal-title">
    <div class="modal-content" id="input-modal-content">
      <!-- Input modal contents set dynamically -->
    </div>
  </div>
  <script>
    // ========== Core App State & Persistence ==========

    // Initialize main app state (schema matches Memory Bank systemPatterns)
    let appState = {
      schemaVersion: 1,
      storyBible: {
        // Array of characters:
        // { id, name, role, appearance, personality, goals, flaws, description }
        characters: [],
        // Array of world elements:
        // { id, name, type, description, significance }
        worldbuilding: [],
        braindump: "",
        genre: "",
        style: "",
        synopsis: "",
        outline: ""
      }
    };

    // For development/testing: populate appState with example data (call this manually as needed)
    function setExampleData() {
      appState.storyBible.characters = [
        {
          id: "char-1",
          name: "Ava Ryland",
          role: "Protagonist",
          appearance: "Short, dark hair, always wears a red scarf.",
          personality: "Analytical, reserved, deeply empathetic.",
          goals: "Solve her brother's disappearance.",
          flaws: "Distrustful; avoids help.",
          description: "Ava is a 32-year-old detective haunted by her past; sharp-minded but emotionally withdrawn."
        },
        {
          id: "char-2",
          name: "Milo",
          role: "Mentor",
          appearance: "Tall, silver-haired, calm aura.",
          personality: "Patient, wise, and cryptic.",
          goals: "Guide Ava from the shadows.",
          flaws: "Secretive, withholds vital truths.",
          description: "Milo is an enigmatic figure with a hidden connection to Ava's family."
        }
      ];
      appState.storyBible.worldbuilding = [
        {
          id: "loc-1",
          name: "Evershade City",
          type: "Location",
          description: "A sprawling metropolis ringed by forests and plagued by persistent rain.",
          significance: "Home to both protagonist and antagonist; holds clues to family secrets."
        },
        {
          id: "faction-1",
          name: "Night Watch",
          type: "Faction",
          description: "A semi-secret collective of freelance detectives.",
          significance: "Provided Ava with protection and training after her childhood trauma."
        }
      ];
      appState.storyBible.braindump = "Ava's obsession is self-destructive. Small-town secrets surface.";
      appState.storyBible.genre = "Neo-noir mystery";
      appState.storyBible.style = "Introspective, atmospheric, terse dialogue.";
      appState.storyBible.synopsis = "Detective Ava’s search for her brother draws her into a citywide conspiracy.";
      appState.storyBible.outline = "- Ava receives a clue\n- Night Watch intervenes\n- Climax at the hidden tunnels";
    }

    // --------- Persistence: localStorage load/save ---------
    const LS_KEY = "pseudowrite-appstate";

    function getLocalStorageBytesUsed() {
      let total = 0;
      for (let key in localStorage) {
        if (!localStorage.hasOwnProperty(key)) continue;
        // Only account for string keys, as localStorage may have prototype keys in some browsers
        try {
          total += (key.length + (localStorage[key]?.length || 0));
        } catch {}
      }
      return total;
    }

    function saveAppState() {
      try {
        localStorage.setItem(LS_KEY, JSON.stringify(appState));
        // Quota pre-warning: 5,000,000 bytes is a rough safe maximum in modern desktop browsers.
        const bytes = getLocalStorageBytesUsed();
        const limit = 5_000_000;
        if (bytes > limit * 0.8) {
          setToolbarStatus(
            "⚠️ Storage almost full! (" +
              (bytes / 1024).toFixed(1) +
              " KB of ~5000 KB used) — Export your project soon and consider clearing old data."
          );
        }
      } catch (e) {
        // Show user-facing alert for quota/localStorage issues, with actionable advice
        setToolbarStatus(
          "❌ Local save error: " + (e.name || "unknown") +
          ". Changes may not persist. " +
          (e.name === "QuotaExceededError"
            ? "Storage limit reached – Export your project and clear old data via browser settings."
            : "Try exporting your project or clearing storage space."
          )
        );
        // Optionally, log error for dev
        console.error("localStorage error:", e);
      }
    }

function loadAppState() {
  const raw = localStorage.getItem(LS_KEY);
  if (raw) {
    try {
      const loaded = JSON.parse(raw);
      if (
        loaded &&
        loaded.storyBible &&
        typeof loaded.schemaVersion === "number"
      ) {
        appState = loaded;
      } else {
        appState = { ...appState, ...loaded };
      }
    } catch (e) {
      setToolbarStatus(
        "❌ Failed to load your data from localStorage. " +
        "Your project will use a blank state. " +
        "Try exporting your data as soon as possible and check browser storage settings!"
      );
      console.warn("Failed to parse app state, using empty state.", e);
    }
  }
}

    // For development only: wipe storage and reload sample data
function devResetAndSeed() {
  localStorage.removeItem(LS_KEY);
  setExampleData();
  saveAppState();
  setTimeout(() => window.location.reload(), 150); // Delay allows localStorage to flush before reload
}

    loadAppState();

    // --------- Placeholder for rendering logic ---------

    // ------- Modal Logic -------
    let modalOpen = false;
    let modalData = null;
    let modalKind = null; // "character" or "world"

    // Input modal state
    let inputModalOpen = false;
    let inputModalCallback = null;

    // Global input modal key handler
    function inputModalKeyHandler(e) {
      if (e.key === "Escape" && inputModalOpen) {
        closeInputModal();
        return;
      }
    }

    function openDetailModal(kind, id) {
      console.log("[DEBUG openDetailModal] called with", kind, id);
      modalOpen = true;
      modalKind = kind;
      window._pdebug = { modalKind: kind, id, modalOpen, modalDataIsNull: false };
      if (kind === "character") {
        modalData = (appState.storyBible.characters || []).find((c) => c.id === id) || null;
      } else if (kind === "world") {
        modalData = (appState.storyBible.worldbuilding || []).find((w) => w.id === id) || null;
      } else {
        modalData = null;
      }
      window._pdebug.modalDataIsNull = modalData == null;
      showDebugStatus(`[openDetailModal] modalKind: ${modalKind}, id: ${id}, modalData null: ${modalData == null}, from pending: ${window.pendingModalToOpen ? JSON.stringify(window.pendingModalToOpen) : ''}, at ${Date.now()}`);
      if (modalData) {
        const ed = document.getElementById('main-editor');
        if (ed) ed.textContent = '[DEBUG] openDetailModal OK for ' + kind + ' id ' + id + ' at ' + Date.now();
      } else {
        const ed = document.getElementById('main-editor');
        if (ed) ed.textContent = '[DEBUG] openDetailModal NO DATA for ' + kind + ' id ' + id + ' at ' + Date.now();
      }
      renderModals();
    }

function closeModal() {
  modalOpen = false;
  modalKind = null;
  modalData = null;
  renderModals();
  renderSidebar();
}

    // Input modal functions
    function openInputModal(title, fields, callback) {
      console.log("[DEBUG openInputModal] called with title:", title);
      showDebugStatus(`[openInputModal] Opening modal: ${title} at ${Date.now()}`);
      inputModalOpen = true;
      inputModalCallback = callback;
      renderInputModal(title, fields);
    }

    function closeInputModal() {
      inputModalOpen = false;
      inputModalCallback = null;
      const modalRoot = document.getElementById("input-modal");
      if (modalRoot) {
        modalRoot.style.display = "none";
      }
      document.removeEventListener("keydown", inputModalKeyHandler);
    }

    function renderInputModal(title, fields) {
      console.log("[DEBUG renderInputModal] called with title:", title, "fields:", fields);
      const modalRoot = document.getElementById("input-modal");
      const modalContent = document.getElementById("input-modal-content");
      if (!modalRoot || !modalContent) {
        console.error("[DEBUG renderInputModal] Modal elements not found!", { modalRoot, modalContent });
        showDebugStatus(`[ERROR] Input modal elements not found at ${Date.now()}`);
        return;
      }
      showDebugStatus(`[renderInputModal] Rendering modal: ${title} at ${Date.now()}`);

      modalRoot.style.display = "flex";
      modalContent.innerHTML = "";

      // Title
      const titleEl = document.createElement("h2");
      titleEl.textContent = title;
      titleEl.id = "input-modal-title";
      titleEl.style.marginTop = "0";
      titleEl.style.marginBottom = "1.2rem";
      titleEl.style.color = "#294ac5";
      modalContent.appendChild(titleEl);

      // Form
      const form = document.createElement("form");
      form.style.display = "flex";
      form.style.flexDirection = "column";
      form.style.gap = "1rem";

      const inputs = {};
      fields.forEach(field => {
        const row = document.createElement("div");
        row.style.display = "flex";
        row.style.flexDirection = "column";

        const label = document.createElement("label");
        label.textContent = field.label + (field.required ? " *" : "");
        label.htmlFor = `input-field-${field.key}`;
        label.style.marginBottom = "0.3rem";
        label.style.fontWeight = "bold";
        label.style.color = "#353b5c";

        const input = document.createElement("input");
        input.type = "text";
        input.name = field.key;
        input.id = `input-field-${field.key}`;
        input.placeholder = field.placeholder || "";
        input.value = field.defaultValue || "";
        input.required = field.required || false;
        input.style.padding = "0.5rem";
        input.style.fontSize = "1rem";
        input.style.borderRadius = "3px";
        input.style.border = "1px solid #bcbccc";
        input.style.background = "#f2f2f8";

        inputs[field.key] = input;
        row.appendChild(label);
        row.appendChild(input);
        form.appendChild(row);
      });

      // Buttons
      const btnRow = document.createElement("div");
      btnRow.style.display = "flex";
      btnRow.style.justifyContent = "flex-end";
      btnRow.style.gap = "0.5rem";
      btnRow.style.marginTop = "1rem";

      const cancelBtn = document.createElement("button");
      cancelBtn.type = "button";
      cancelBtn.textContent = "Cancel";
      cancelBtn.style.padding = "0.4rem 1.1rem";
      cancelBtn.style.background = "#eee";
      cancelBtn.style.color = "#222";
      cancelBtn.style.border = "none";
      cancelBtn.style.borderRadius = "3px";
      cancelBtn.style.fontWeight = "bold";
      cancelBtn.style.cursor = "pointer";
      cancelBtn.onclick = closeInputModal;

      const submitBtn = document.createElement("button");
      submitBtn.type = "submit";
      submitBtn.textContent = "Create";
      submitBtn.style.padding = "0.4rem 1.3rem";
      submitBtn.style.background = "#507eef";
      submitBtn.style.color = "#fff";
      submitBtn.style.border = "none";
      submitBtn.style.borderRadius = "3px";
      submitBtn.style.fontWeight = "bold";
      submitBtn.style.cursor = "pointer";

      btnRow.appendChild(cancelBtn);
      btnRow.appendChild(submitBtn);
      form.appendChild(btnRow);

      form.onsubmit = (e) => {
        e.preventDefault();
        const values = {};
        fields.forEach(field => {
          values[field.key] = inputs[field.key].value.trim();
        });

        // Validate required fields
        const missingRequired = fields.filter(field => field.required && !values[field.key]);
        if (missingRequired.length > 0) {
          alert(`Please fill in required fields: ${missingRequired.map(f => f.label).join(', ')}`);
          return;
        }

        closeInputModal();
        if (inputModalCallback) {
          inputModalCallback(values);
        }
      };

      modalContent.appendChild(form);

      // Focus first input
      setTimeout(() => {
        const firstInput = form.querySelector("input");
        if (firstInput) firstInput.focus();
      }, 10);

      // Set up keyboard handling
      document.removeEventListener("keydown", inputModalKeyHandler);
      document.addEventListener("keydown", inputModalKeyHandler);
    }

    function saveModalEdits(newData) {
      if (modalKind === "character") {
        const idx = appState.storyBible.characters.findIndex((c) => c.id === newData.id);
        if (idx !== -1) appState.storyBible.characters[idx] = { ...newData };
      } else if (modalKind === "world") {
        const idx = appState.storyBible.worldbuilding.findIndex((w) => w.id === newData.id);
        if (idx !== -1) appState.storyBible.worldbuilding[idx] = { ...newData };
      }
      saveAppState();
      closeModal();
      renderSidebar();
    }
    
    function renderSidebar() {
      const nav = document.getElementById("sidebar-nav");
      if (!nav) return;
      showDebugStatus(`[renderSidebar] Start. pendingModalToOpen: ${window.pendingModalToOpen ? JSON.stringify(window.pendingModalToOpen) : 'null'}, at ${Date.now()}`);

      // Clear any existing content
      nav.innerHTML = "";
      // After clearing nav, check for deferred modal opening
      if (window.pendingModalToOpen) {
        showDebugStatus(`[renderSidebar] Trigger openDetailModal for pending: ${JSON.stringify(window.pendingModalToOpen)} at ${Date.now()}`);
        openDetailModal(window.pendingModalToOpen.kind, window.pendingModalToOpen.id);
        delete window.pendingModalToOpen;
      }

      // Group characters by role
      const chars = appState.storyBible.characters || [];
      const roles = {};
      chars.forEach((c) => {
        if (!roles[c.role]) roles[c.role] = [];
        roles[c.role].push(c);
      });

      // Render character accordion
      const charHeader = document.createElement("div");
      charHeader.style.margin = "1rem 0 0.35rem 0.9rem";
      charHeader.style.color = "#b8c6e0";
      charHeader.style.display = "flex";
      charHeader.style.alignItems = "center";
      charHeader.textContent = "Characters";

      // Quick-add (+) button
      const charAddBtn = document.createElement("button");
      charAddBtn.textContent = "+";
      charAddBtn.title = "Add new Character";
      charAddBtn.style.marginLeft = "0.55em";
      charAddBtn.style.background = "#365fdc";
      charAddBtn.style.color = "#fff";
      charAddBtn.style.border = "none";
      charAddBtn.style.borderRadius = "50%";
      charAddBtn.style.width = "1.38em";
      charAddBtn.style.height = "1.38em";
      charAddBtn.style.fontWeight = "bold";
      charAddBtn.style.cursor = "pointer";
      charAddBtn.style.fontSize = "1.1em";
      charAddBtn.style.display = "flex";
      charAddBtn.style.alignItems = "center";
      charAddBtn.style.justifyContent = "center";
      charAddBtn.style.outline = "none";
      charAddBtn.tabIndex = 0;
showDebugStatus("[ATTACH] charAddBtn handler attached at " + Date.now());
charAddBtn.addEventListener("click", () => {
        console.log("[DEBUG +] Handler FIRED");
        showDebugStatus("[FIRE] charAddBtn handler fired at " + Date.now());
        const ed = document.getElementById('main-editor');
        if (ed) ed.textContent = '[DEBUG] + fired at ' + Date.now();

        // Open input modal for character creation
        openInputModal("Create New Character", [
          { key: "name", label: "Character Name", required: true, placeholder: "Enter character name" },
          { key: "role", label: "Role", required: false, placeholder: "e.g., Protagonist, Antagonist, etc.", defaultValue: "Character" }
        ], (values) => {
          const id = "char-" + Date.now();
          const newChar = {
            id,
            name: values.name,
            role: values.role || "Character",
            appearance: "",
            personality: "",
            goals: "",
            flaws: "",
            description: ""
          };
          appState.storyBible.characters.push(newChar);
          saveAppState();
          window.pendingModalToOpen = { kind: "character", id };
          console.log("[DEBUG +] Handler: Set pendingModalToOpen", window.pendingModalToOpen);
          showDebugStatus("[STATE] new character created, set pendingModalToOpen for " + id + " at " + Date.now());
          if (ed) ed.textContent = '[DEBUG] Created char id ' + id + " at " + Date.now();
          renderSidebar();
        });
      });
      charHeader.appendChild(charAddBtn);
      nav.appendChild(charHeader);

      Object.entries(roles).forEach(([role, arr], idx) => {
        const section = document.createElement("section");
        section.style.marginBottom = "0.5rem";

        // Accordion header
        const toggle = document.createElement("button");
        toggle.type = "button";
        toggle.textContent = `${role} (${arr.length})`;
        toggle.style.background = "#363656";
        toggle.style.border = "none";
        toggle.style.color = "#e2e7ff";
        toggle.style.fontWeight = "bold";
        toggle.style.width = "100%";
        toggle.style.textAlign = "left";
        toggle.style.padding = "0.5rem 0.9rem";
        toggle.style.cursor = "pointer";
        toggle.style.outline = "none";
        toggle.style.borderRadius = "5px 5px 0 0";
        section.appendChild(toggle);

        // Content (list of characters)
        const content = document.createElement("div");
        content.style.display = "block";
        content.style.background = "#222236";
        content.style.padding = "0.14rem 0 0.2rem 1.4rem";
        content.style.transition = "0.2s";
        arr.forEach((c) => {
          const item = document.createElement("div");
          item.textContent = c.name;
          item.style.cursor = "pointer";
          item.style.margin = "0.11rem 0";
          item.style.padding = "0.18rem 0.2rem";
          item.setAttribute("data-char-id", c.id);
          item.onclick = () => {
            modalOpen = true;
            openDetailModal("character", c.id);
          };
          item.title = "Edit character details";
          item.style.borderRadius = "3px";
          item.onmouseenter = () => (item.style.background = "#32325b");
          item.onmouseleave = () => (item.style.background = "transparent");
          content.appendChild(item);
        });
        section.appendChild(content);

        // Toggle logic
        let open = true;
        toggle.onclick = () => {
          open = !open;
          content.style.display = open ? "block" : "none";
          toggle.style.borderRadius = open ? "5px 5px 0 0" : "5px";
        };

        nav.appendChild(section);
      });

      if (chars.length === 0) {
        const none = document.createElement("div");
        none.textContent = "No characters yet.";
        none.style.color = "#8a99a3";
        none.style.fontStyle = "italic";
        none.style.marginLeft = "1.3rem";
        nav.appendChild(none);
      }

      // Group worldbuilding by type
      const worlds = appState.storyBible.worldbuilding || [];
      const types = {};
      worlds.forEach((w) => {
        if (!types[w.type]) types[w.type] = [];
        types[w.type] = types[w.type] || [];
        types[w.type].push(w);
      });

      // Render worldbuilding accordion
      const worldHeader = document.createElement("div");
      worldHeader.style.margin = "1rem 0 0.35rem 0.9rem";
      worldHeader.style.color = "#c8d8cf";
      worldHeader.style.display = "flex";
      worldHeader.style.alignItems = "center";
      worldHeader.textContent = "Worldbuilding";

      // Quick-add (+) button for Worldbuilding
      const worldAddBtn = document.createElement("button");
      worldAddBtn.textContent = "+";
      worldAddBtn.title = "Add new World element";
      worldAddBtn.style.marginLeft = "0.55em";
      worldAddBtn.style.background = "#399d45";
      worldAddBtn.style.color = "#fff";
      worldAddBtn.style.border = "none";
      worldAddBtn.style.borderRadius = "50%";
      worldAddBtn.style.width = "1.38em";
      worldAddBtn.style.height = "1.38em";
      worldAddBtn.style.fontWeight = "bold";
      worldAddBtn.style.cursor = "pointer";
      worldAddBtn.style.fontSize = "1.1em";
      worldAddBtn.style.display = "flex";
      worldAddBtn.style.alignItems = "center";
      worldAddBtn.style.justifyContent = "center";
      worldAddBtn.style.outline = "none";
      worldAddBtn.tabIndex = 0;
showDebugStatus("[ATTACH] worldAddBtn handler attached at " + Date.now());
worldAddBtn.addEventListener("click", () => {
        showDebugStatus("[FIRE] worldAddBtn handler fired at " + Date.now());

        // Open input modal for worldbuilding creation
        openInputModal("Create New World Element", [
          { key: "name", label: "Element Name", required: true, placeholder: "Enter element name" },
          { key: "type", label: "Element Type", required: false, placeholder: "e.g., Location, Faction, Item", defaultValue: "World" }
        ], (values) => {
          const id = "world-" + Date.now();
          const newWorld = {
            id,
            name: values.name,
            type: values.type || "World",
            description: "",
            significance: ""
          };
          appState.storyBible.worldbuilding.push(newWorld);
          saveAppState();
          window.pendingModalToOpen = { kind: "world", id };
          showDebugStatus("[STATE] new world element created, set pendingModalToOpen for " + id + " at " + Date.now());
          renderSidebar();
        });
      });
      worldHeader.appendChild(worldAddBtn);
      nav.appendChild(worldHeader);

      Object.entries(types).forEach(([type, arr], idx) => {
        const section = document.createElement("section");
        section.style.marginBottom = "0.5rem";

        // Accordion header
        const toggle = document.createElement("button");
        toggle.type = "button";
        toggle.textContent = `${type} (${arr.length})`;
        toggle.style.background = "#43563e";
        toggle.style.border = "none";
        toggle.style.color = "#e2ffe2";
        toggle.style.fontWeight = "bold";
        toggle.style.width = "100%";
        toggle.style.textAlign = "left";
        toggle.style.padding = "0.5rem 0.9rem";
        toggle.style.cursor = "pointer";
        toggle.style.outline = "none";
        toggle.style.borderRadius = "5px 5px 0 0";
        section.appendChild(toggle);

        // Content (list of items)
        const content = document.createElement("div");
        content.style.display = "block";
        content.style.background = "#123416";
        content.style.padding = "0.14rem 0 0.2rem 1.4rem";
        arr.forEach((w) => {
          const item = document.createElement("div");
          item.textContent = w.name;
          item.style.cursor = "pointer";
          item.style.margin = "0.11rem 0";
          item.style.padding = "0.18rem 0.2rem";
          item.setAttribute("data-world-id", w.id);
          item.onclick = () => {
            modalOpen = true;
            openDetailModal("world", w.id);
          };
          item.title = "Edit worldbuilding details";
          item.style.borderRadius = "3px";
          item.onmouseenter = () => (item.style.background = "#185c28");
          item.onmouseleave = () => (item.style.background = "transparent");
          content.appendChild(item);
        });
        section.appendChild(content);

        // Toggle logic
        let open = true;
        toggle.onclick = () => {
          open = !open;
          content.style.display = open ? "block" : "none";
          toggle.style.borderRadius = open ? "5px 5px 0 0" : "5px";
        };

        nav.appendChild(section);
      });

      if (worlds.length === 0) {
        const none = document.createElement("div");
        none.textContent = "No worldbuilding elements yet.";
        none.style.color = "#b5bfae";
        none.style.fontStyle = "italic";
        none.style.marginLeft = "1.3rem";
        nav.appendChild(none);
      }
    }

    function renderModals() {
      const modalRoot = document.getElementById("detail-modal");
      const modalContent = document.getElementById("modal-content");
      if (!modalRoot || !modalContent) return;

      showDebugStatus(`[renderModals] modalOpen: ${modalOpen}, modalKind: ${modalKind}, modalData null: ${modalData==null}, at ${Date.now()}`);

      if (!modalOpen || !modalData) {
        modalRoot.style.display = "none";
        modalContent.innerHTML = "";
        document.removeEventListener("keydown", modalKeyHandler);
        return;
      }

      // Build modal fields
      modalRoot.style.display = "flex";
      modalContent.innerHTML = "";
      let fields = [];
      if (modalKind === "character") {
        fields = [
          { label: "Name", key: "name" },
          { label: "Role", key: "role" },
          { label: "Appearance", key: "appearance" },
          { label: "Personality", key: "personality" },
          { label: "Goals", key: "goals" },
          { label: "Flaws", key: "flaws" },
          { label: "Description", key: "description" }
        ];
      } else if (modalKind === "world") {
        fields = [
          { label: "Name", key: "name" },
          { label: "Type", key: "type" },
          { label: "Description", key: "description" },
          { label: "Significance", key: "significance" }
        ];
      }

      const form = document.createElement("form");
      form.style.display = "flex";
      form.style.flexDirection = "column";
      form.style.gap = "1rem";
      form.onsubmit = (e) => {
        e.preventDefault();
        // Read out all field values and save
        const newData = { ...modalData };
        fields.forEach(f => {
          const el = form.querySelector(`[name=${f.key}]`);
          if (el) newData[f.key] = el.value;
        });
        saveModalEdits(newData);
      };

      fields.forEach(f => {
        const row = document.createElement("div");
        row.style.display = "flex";
        row.style.flexDirection = "column";
        const label = document.createElement("label");
        label.textContent = f.label;
        label.htmlFor = `modal-field-${f.key}`;
        label.style.marginBottom = "0.06rem";
        label.style.fontWeight = "bold";
        label.style.color = "#353b5c";
        const input = f.key === "description"
          ? document.createElement("textarea")
          : document.createElement("input");
        input.name = f.key;
        input.id = `modal-field-${f.key}`;
        input.value = modalData[f.key] || "";
        input.style.padding = "0.5rem";
        input.style.fontSize = "1rem";
        input.style.marginBottom = "0.13rem";
        input.style.borderRadius = "3px";
        input.style.border = "1px solid #bcbccc";
        input.style.background = "#f2f2f8";
        if (f.key === "description") {
          input.rows = 3;
        }
        row.appendChild(label);
        row.appendChild(input);
        form.appendChild(row);
      });

      // Modal footer with actions
      const btnRow = document.createElement("div");
      btnRow.style.display = "flex";
      btnRow.style.justifyContent = "flex-end";
      btnRow.style.gap = "0.5rem";
      btnRow.style.marginTop = "1rem";

      // Delete button, only for editing an existing object
      if (modalKind === "character" && modalData && modalData.id) {
        const del = document.createElement("button");
        del.type = "button";
        del.textContent = "Delete";
        del.title = "Delete this character";
        del.style.background = "#e74c3c";
        del.style.color = "#fff";
        del.style.border = "none";
        del.style.padding = "0.4rem 1.1rem";
        del.style.borderRadius = "3px";
        del.style.fontWeight = "bold";
        del.style.cursor = "pointer";
        del.onclick = function() {
          if (confirm("Delete this character? This cannot be undone.")) {
            appState.storyBible.characters = appState.storyBible.characters.filter(c => c.id !== modalData.id);
            saveAppState();
            closeModal();
            renderSidebar();
          }
        };
        btnRow.appendChild(del);
      }
      if (modalKind === "world" && modalData && modalData.id) {
        const del = document.createElement("button");
        del.type = "button";
        del.textContent = "Delete";
        del.title = "Delete this world element";
        del.style.background = "#e74c3c";
        del.style.color = "#fff";
        del.style.border = "none";
        del.style.padding = "0.4rem 1.1rem";
        del.style.borderRadius = "3px";
        del.style.fontWeight = "bold";
        del.style.cursor = "pointer";
        del.onclick = function() {
          if (confirm("Delete this world element? This cannot be undone.")) {
            appState.storyBible.worldbuilding = appState.storyBible.worldbuilding.filter(w => w.id !== modalData.id);
            saveAppState();
            closeModal();
            renderSidebar();
          }
        };
        btnRow.appendChild(del);
      }

      const cancel = document.createElement("button");
      cancel.type = "button";
      cancel.textContent = "Cancel";
      cancel.onclick = closeModal;
      cancel.style.padding = "0.4rem 1.1rem";
      cancel.style.background = "#eee";
      cancel.style.color = "#222";
      cancel.style.border = "none";
      cancel.style.borderRadius = "3px";
      cancel.style.fontWeight = "bold";
      cancel.style.cursor = "pointer";

      const save = document.createElement("button");
      save.type = "submit";
      save.textContent = "Save";
      save.style.padding = "0.4rem 1.3rem";
      save.style.background = "#507eef";
      save.style.color = "#fff";
      save.style.border = "none";
      save.style.borderRadius = "3px";
      save.style.fontWeight = "bold";
      save.style.cursor = "pointer";

      btnRow.appendChild(cancel);
      btnRow.appendChild(save);
      form.appendChild(btnRow);

      // Title bar
      const title = document.createElement("h2");
      title.textContent = modalKind === "character" ? "Edit Character" : "Edit World Element";
      title.id = "modal-title";
      title.setAttribute("tabindex", "-1");
      title.style.marginTop = "0";
      title.style.marginBottom = "1.2rem";
      title.style.color = "#294ac5";
      modalContent.appendChild(title);

      modalContent.appendChild(form);

      // Keyboard accessibility: trap tab/focus inside modal, esc to close
      setTimeout(() => {
        const focusables = modalRoot.querySelectorAll("input, textarea, button, select, [tabindex]");
        if (focusables.length) {
          focusables[0].focus();
        }
      }, 10);

      function modalKeyHandler(e) {
        // ESC to close
        if (e.key === "Escape") {
          closeModal();
          return;
        }
        // Tab/Shift+Tab loop
        const focusables = modalRoot.querySelectorAll("input, textarea, button, select, [tabindex]");
        if (focusables.length > 0 && (e.key === "Tab")) {
          const arr = Array.from(focusables);
          const idx = arr.indexOf(document.activeElement);
          if (e.shiftKey) {
            // Move focus backwards
            if (idx === 0 || document.activeElement === modalRoot) {
              arr[arr.length - 1].focus();
              e.preventDefault();
            }
          } else {
            // Move focus forwards
            if (idx === arr.length - 1) {
              arr[0].focus();
              e.preventDefault();
            }
          }
        }
      }
      document.removeEventListener("keydown", modalKeyHandler);
      document.addEventListener("keydown", modalKeyHandler);
    }

    // --------- Toolbar AI stub logic ---------
    function setToolbarStatus(msg) {
      const s = document.getElementById("toolbar-status");
      if (s) s.textContent = msg;
    }

    let aiBusy = false;

    // Pollinations API integration for "Write"
    const POLLINATIONS_URL = "https://text.pollinations.ai/openai/v1/chat/completions"; // update if needed
    const POLLINATIONS_MODEL = "gpt-3.5-turbo"; // or another supported

    async function runWriteAI() {
      if (aiBusy) return;
      aiBusy = true;
      setToolbarStatus("⏳ Write: contacting AI...");

      // Disable all buttons while in flight
      const all = [
        document.getElementById("btn-write"),
        document.getElementById("btn-rewrite"),
        document.getElementById("btn-describe")
      ];
      all.forEach(b => { if (b) b.disabled = true; });

      try {
        // Compose full prompt using current draft and Story Bible
        const context = `
== STORY BIBLE ==
${JSON.stringify(appState.storyBible, null, 2)}

== USER DRAFT (up to cursor) ==
${document.getElementById("main-editor").textContent || ""}

# Task:
Continue this story in the author's style, one paragraph at a time. Focus on relevance, forward motion, and voice.
        `.trim();

        const res = await fetch(POLLINATIONS_URL, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            model: POLLINATIONS_MODEL,
            messages: [{ role: "user", content: context }],
            max_tokens: 300,
            temperature: 0.8
          })
        });

        if (!res.ok) throw new Error("AI call failed");
        const data = await res.json();
        const aiText = data.choices?.[0]?.message?.content?.trim() || "";
        if (!aiText || aiText === "[No AI result]") {
          setToolbarStatus(
            "❌ Write failed: AI did not return a usable result. Please try again, check your draft, or refresh the page."
          );
        } else {
          insertAIText(aiText);
          setToolbarStatus("✔️ Write complete");
        }
      } catch (e) {
        setToolbarStatus(
          "❌ AI error: " + (e.message || e) +
          ". Check your Internet connection and retry. If this persists, try refreshing the page or simplifying the prompt."
        );
      } finally {
        all.forEach(b => { if (b) b.disabled = false; });
        aiBusy = false;
        setTimeout(() => setToolbarStatus(""), 1800);
      }
    }

    function insertAIText(aiText) {
      const editor = document.getElementById("main-editor");
      if (!editor) return;
      // Find cursor and insert at caret
      let sel = window.getSelection();
      let range = sel && sel.rangeCount > 0 ? sel.getRangeAt(0) : null;

      if (range && editor.contains(range.startContainer)) {
        // Insert at caret
        range.deleteContents();
        const node = document.createTextNode(aiText);
        range.insertNode(node);

        // Move caret after inserted node
        range.setStartAfter(node);
        range.collapse(true);
        sel.removeAllRanges();
        sel.addRange(range);
      } else {
        // Fallback: append to end
        editor.append(aiText);
      }

      // Sync to state and save
      syncStateFromEditor();
    }

    // --- Helper: Get selection or all ---
    function getEditorSelectionOrContent() {
      const ed = document.getElementById("main-editor");
      let text = "";
      if (!ed) return "";
      const sel = window.getSelection();
      if (
        sel &&
        sel.rangeCount > 0 &&
        ed.contains(sel.anchorNode) &&
        !sel.isCollapsed
      ) {
        text = sel.toString();
      }
      if (!text) text = ed.textContent || "";
      return text;
    }

    // --- Rewrite (AI) ---
    async function runRewriteAI() {
      if (aiBusy) return;
      aiBusy = true;
      setToolbarStatus("⏳ Rewrite: contacting AI...");
      const all = [
        document.getElementById("btn-write"),
        document.getElementById("btn-rewrite"),
        document.getElementById("btn-describe")
      ];
      all.forEach(b => { if (b) b.disabled = true; });
      try {
        const selectedText = getEditorSelectionOrContent();
        const context = `
== STORY BIBLE ==
${JSON.stringify(appState.storyBible, null, 2)}

== TO REWRITE ==
${selectedText}

# Task:
Rewrite the above passage in a different style, improving pacing/imagery, but maintain meaning and voice. Output only the rewritten passage.
        `.trim();

        const res = await fetch(POLLINATIONS_URL, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            model: POLLINATIONS_MODEL,
            messages: [{ role: "user", content: context }],
            max_tokens: 260,
            temperature: 0.85
          })
        });
        if (!res.ok) throw new Error("AI call failed");
        const data = await res.json();
        const aiText = data.choices?.[0]?.message?.content?.trim() || "";
        if (!aiText || aiText === "[No AI result]") {
          setToolbarStatus(
            "❌ Rewrite failed: AI did not return a usable result. Please try again, check your passage, or refresh the page."
          );
        } else {
          insertRewriteAIResult(aiText);
          setToolbarStatus("✔️ Rewrite complete");
        }
      } catch (e) {
        setToolbarStatus(
          "❌ AI error: " + (e.message || e) +
          ". Check your Internet connection and retry. If this persists, try refreshing the page or simplifying the passage."
        );
      } finally {
        all.forEach(b => { if (b) b.disabled = false; });
        aiBusy = false;
        setTimeout(() => setToolbarStatus(""), 1800);
      }
    }

    function insertRewriteAIResult(aiText) {
      const editor = document.getElementById("main-editor");
      if (!editor) return;
      let sel = window.getSelection();
      let range = sel && sel.rangeCount > 0 ? sel.getRangeAt(0) : null;
      // Replace selection if present, else append
      if (
        range &&
        editor.contains(range.startContainer) &&
        !sel.isCollapsed
      ) {
        range.deleteContents();
        range.insertNode(document.createTextNode(aiText));
        // Move caret after inserted node
        range.collapse(false);
        sel.removeAllRanges();
        sel.addRange(range);
      } else {
        editor.append(aiText);
      }
      syncStateFromEditor();
    }

    // --- Describe (AI) ---
    async function runDescribeAI() {
      if (aiBusy) return;
      aiBusy = true;
      setToolbarStatus("⏳ Describe: contacting AI...");
      const all = [
        document.getElementById("btn-write"),
        document.getElementById("btn-rewrite"),
        document.getElementById("btn-describe")
      ];
      all.forEach(b => { if (b) b.disabled = true; });
      try {
        const selectedText = getEditorSelectionOrContent();
        const context = `
== STORY BIBLE ==
${JSON.stringify(appState.storyBible, null, 2)}

== TO DESCRIBE ==
${selectedText}

# Task:
Expand on the above passage, injecting vivid, multi-sensory description and emotional detail. Output only the revised/expanded passage.
        `.trim();

        const res = await fetch(POLLINATIONS_URL, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            model: POLLINATIONS_MODEL,
            messages: [{ role: "user", content: context }],
            max_tokens: 260,
            temperature: 0.90
          })
        });
        if (!res.ok) throw new Error("AI call failed");
        const data = await res.json();
        const aiText = data.choices?.[0]?.message?.content?.trim() || "";
        if (!aiText || aiText === "[No AI result]") {
          setToolbarStatus(
            "❌ Describe failed: AI did not return a usable result. Please try again, check your request, or refresh the page."
          );
        } else {
          insertRewriteAIResult(aiText);
          setToolbarStatus("✔️ Describe complete");
        }
      } catch (e) {
        setToolbarStatus(
          "❌ AI error: " + (e.message || e) +
          ". Check your Internet connection and retry. If this persists, try refreshing the page or making your request shorter."
        );
      } finally {
        all.forEach(b => { if (b) b.disabled = false; });
        aiBusy = false;
        setTimeout(() => setToolbarStatus(""), 1800);
      }
    }

    function runToolStub(tool) {
      if (aiBusy) return;
      aiBusy = true;
      setToolbarStatus(`⏳ ${tool} (AI call not yet implemented)...`);
      // Simulate loading
      const all = [
        document.getElementById("btn-write"),
        document.getElementById("btn-rewrite"),
        document.getElementById("btn-describe")
      ];
      all.forEach(b => { if (b) b.disabled = true; });
      setTimeout(() => {
        setToolbarStatus("");
        all.forEach(b => { if (b) b.disabled = false; });
        aiBusy = false;
      }, 1100);
    }

function showDebugStatus(msg) {
  let el = document.getElementById("debug-status");
  if (el) {
    el.textContent = '[PseudoWrite DEBUG] ' + msg;
    el.style.display = "block";
    setTimeout(() => { el.style.display = "none"; }, 8000);
  }
}

// Test function for debugging modal functionality
window.testInputModal = function() {
  console.log("[TEST] Testing input modal...");
  openInputModal("Test Modal", [
    { key: "name", label: "Test Name", required: true, placeholder: "Enter test name" },
    { key: "description", label: "Test Description", required: false, placeholder: "Enter description" }
  ], (values) => {
    console.log("[TEST] Modal callback received values:", values);
    alert("Test successful! Values: " + JSON.stringify(values));
  });
};

window.addEventListener("DOMContentLoaded", () => {
      // DEBUG: Prove JS is running
      console.log("[DEBUG] DOMContentLoaded fired - JS is running");
      // Always ensure appState loads before populating sidebar
      loadAppState();
      const banner = document.getElementById("onboard-banner");
      if (banner) {
        banner.style.display = "none"; // Hide by default for test
        console.log("[DEBUG] Onboard banner set to display:none on load");
      }
      const attemptSidebarRender = (retries = 3) => {
        const nav = document.getElementById("sidebar-nav");
        if (!nav || !Array.isArray(appState.storyBible.characters) || !Array.isArray(appState.storyBible.worldbuilding)) {
          if (retries > 0) {
            setTimeout(() => attemptSidebarRender(retries - 1), 45);
          } else {
            if (typeof console !== "undefined") console.warn("Sidebar population failed: DOM or state not ready.");
          }
        } else {
          renderSidebar();
        }
      };
      attemptSidebarRender();
      const w = document.getElementById("btn-write"),
        r = document.getElementById("btn-rewrite"),
        d = document.getElementById("btn-describe");
      if (w) w.onclick = runWriteAI;
      if (r) r.onclick = runRewriteAI;
      if (d) d.onclick = runDescribeAI;

      // Onboarding/Help logic
      const onboard = document.getElementById("onboard-banner");
      const showHelp = document.getElementById("show-help");
      const hideOnboard = document.getElementById("hide-onboard");
      // Show banner if not dismissed in localStorage
      // (PATCHED: Do not reset to block here; initial visibility handled by CSS and earlier logic.)
      if (hideOnboard) hideOnboard.onclick = () => {
        onboard.style.display = "none";
        localStorage.setItem("pw-onboard-hide", "1");
      };
if (showHelp) showHelp.onclick = () => {
  const onboardLive = document.getElementById("onboard-banner");
  if (!onboardLive) {
    console.warn("[HelpToggle] No onboard banner found!");
    return;
  }
  const cs = window.getComputedStyle(onboardLive);
  const isVisible = cs.display !== "none";
  console.log("[HelpToggle] Current visible?", isVisible, "Display:", cs.display);
  if (isVisible) {
    onboardLive.style.display = "none";
    localStorage.setItem("pw-onboard-hide", "1");
    console.log("[HelpToggle] Set display: none; state = hidden");
  } else {
    onboardLive.style.display = "block";
    localStorage.removeItem("pw-onboard-hide");
    console.log("[HelpToggle] Set display: block; state = visible");
  }
};
      // TESTABILITY: Add ESC dismiss and a global hook (only for DEV/testing)
      if (onboard) {
        window.addEventListener("keydown", function(e) {
          if (e.key === "Escape" && onboard.style.display === "block") {
            onboard.style.display = "none";
            localStorage.setItem("pw-onboard-hide", "1");
          }
        });
        if (window.location.protocol === "file:" || window.location.hostname === "localhost") {
          window.closeOnboardBanner = function() {
            onboard.style.display = "none";
            localStorage.setItem("pw-onboard-hide", "1");
          };
        }
      }
    });

    // --------- Export / Import logic ---------
    function exportAppState() {
      try {
        const raw = JSON.stringify(appState, null, 2);
        const blob = new Blob([raw], { type: "application/json" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "pseudowrite-project.json";
        document.body.appendChild(a);
        a.click();
        setTimeout(() => {
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
          setToolbarStatus("✔️ Project exported.");
        }, 100);
      } catch (e) {
        setToolbarStatus(
          "❌ Export error: " + (e.message || e) +
          ". Try freeing up disk space or using a different browser."
        );
      }
    }

    function importAppState(file) {
      if (!file) return;
      const reader = new FileReader();
      reader.onload = function(e) {
        try {
          const newState = JSON.parse(e.target.result);
          if (!newState.storyBible) throw new Error("Missing storyBible.");
          appState = newState;
          saveAppState();
          renderSidebar();
          syncEditorFromState();
          setToolbarStatus("✔️ Project imported.");
        } catch (err) {
          setToolbarStatus(
            "❌ Invalid file. Could not import: " + (err.message || err) +
            ". Make sure your file is a valid PseudoWrite export (.json) and try again."
          );
        }
      };
      reader.readAsText(file);
    }

    window.addEventListener("DOMContentLoaded", () => {
      const exportBtn = document.getElementById("btn-export");
      const importInput = document.getElementById("file-import");
      if (exportBtn) exportBtn.onclick = exportAppState;
      if (importInput) {
        importInput.addEventListener("change", (e) => {
          const file = e.target.files && e.target.files[0];
          if (file) importAppState(file);
          e.target.value = ""; // reset input for future use
        });
      }
    });

    // --------- Editor sync logic ---------
    // Add a persistent main draft entry to storyBible if not present
    if (!('draft' in appState.storyBible)) {
      appState.storyBible.draft = "";
    }

    function syncEditorFromState() {
      const editor = document.getElementById("main-editor");
      if (editor) {
        editor.textContent = appState.storyBible.draft || "";
      }
    }

    function syncStateFromEditor() {
      const editor = document.getElementById("main-editor");
      if (editor) {
        appState.storyBible.draft = editor.textContent || "";
        saveAppState();
      }
    }

    // Set up editor input sync on load
    window.addEventListener("DOMContentLoaded", () => {
      syncEditorFromState();
      const editor = document.getElementById("main-editor");
      if (editor) {
        editor.addEventListener("input", syncStateFromEditor);
      }
    });

    // Always persist on window unload
    window.addEventListener('beforeunload', () => {
      syncStateFromEditor();
      saveAppState();
    });

    // --- Dev UX: Add sample data/reset controls (for initial testing only) ---
    if (window.location.hostname === "localhost" || window.location.protocol === "file:") {
      // Add a dev-only button to the document for demo/reset/testing
      const btn = document.createElement('button');
      btn.textContent = "DEV: Reset & Load Example Data";
      btn.style.position = "fixed";
      btn.style.bottom = "16px";
      btn.style.right = "22px";
      btn.style.zIndex = 3000;
      btn.style.padding = "0.7rem 1.4rem";
      btn.onclick = devResetAndSeed;
      document.body.appendChild(btn);

      // === Automated Test Suite (DEV ONLY) ===
      function logTestResult(msg, pass, error) {
        const node = document.createElement('div');
        node.textContent = (pass ? "✅ " : "❌ ") + msg + (error ? " — " + error : "");
        node.style.color = pass ? "#235d32" : "#ad1f20";
        node.style.fontFamily = "Consolas, monospace";
        node.style.fontSize = "1.03rem";
        document.getElementById('test-suite-log').appendChild(node);
      }

      function runAllTests() {
        const logDiv = document.getElementById('test-suite-log');
        logDiv.innerHTML = "";
        let tests = [
          // Test 1: AI Invalid JSON Handling
          function testInvalidAIJson() {
            // Emulate an AI response with broken JSON and run the parse logic
            try {
              const broken = "{invalid: true, 'not': [1,2,3]}"; // deliberately invalid JSON (should cause JSON.parse to fail)
              let parsed = true;
              try { JSON.parse(broken); } catch { parsed = false; }
              logTestResult("AI Invalid JSON handling", !parsed);
            } catch (e) {
              logTestResult("AI Invalid JSON handling", false, e.message);
            }
            return Promise.resolve();
          },

          // Test 2: LocalStorage Overflow
          function testLocalStorageOverflow() {
            try {
              let err = "";
              try {
                localStorage.setItem("pseudowrite-big", "X".repeat(6 * 1024 * 1024));
              } catch (e) {
                err = e.name;
              }
              saveAppState();
              logTestResult("LocalStorage quota warning fires", err === "QuotaExceededError");
              localStorage.removeItem("pseudowrite-big");
            } catch (e) {
              logTestResult("LocalStorage quota warning fires", false, e.message);
            }
            return Promise.resolve();
          },

          // Test 3: Import Invalid File (simulate toolbar error display)
          function testImportInvalidFile() {
            return new Promise(resolve => {
              setToolbarStatus("❌ Invalid file. Could not import: Simulated error for test.");
              setTimeout(() => {
                let text = document.getElementById('toolbar-status').textContent || "";
                logTestResult("Import invalid file error visible", text.includes("Invalid file"));
                resolve();
              }, 300);
            });
          },

          // Test 4: Sidebar Empty States
          function testEmptySidebarStates() {
            return new Promise(resolve => {
              appState.storyBible.characters = [];
              appState.storyBible.worldbuilding = [];
              renderSidebar();
              setTimeout(() => {
                const nav = document.getElementById('sidebar-nav');
                const text = nav.textContent || "";
                logTestResult("Sidebar empty-state visible (characters)", text.includes("No characters yet."));
                logTestResult("Sidebar empty-state visible (world)", text.includes("No worldbuilding elements yet."));
                resolve();
              }, 150);
            });
          }
        ];
        (async function() {
          for (let t of tests) {
            await t();
          }
        })();
      }

      // --- Test suite UI ---
      const testPanel = document.createElement("section");
      testPanel.style.position = "fixed";
      testPanel.style.bottom = "24px";
      testPanel.style.left = "24px";
      testPanel.style.width = "350px";
      testPanel.style.background = "#222";
      testPanel.style.color = "#fff";
      testPanel.style.padding = "1rem 1.3rem 0.4rem 1.1rem";
      testPanel.style.boxShadow = "0 2px 12px #0006";
      testPanel.style.borderRadius = "8px";
      testPanel.style.zIndex = 4000;

      const header = document.createElement("div");
      header.textContent = "Automated QA (DEV Only)";
      header.style.fontWeight = "bold";
      header.style.fontSize = "1.19rem";
      header.style.marginBottom = "0.5rem";

      const runBtn = document.createElement("button");
      runBtn.textContent = "Run Tests";
      runBtn.style.background = "#4aaf50";
      runBtn.style.color = "#fff";
      runBtn.style.padding = "0.44rem 0.92rem";
      runBtn.style.border = "none";
      runBtn.style.borderRadius = "4px";
      runBtn.style.marginBottom = "0.6rem";
      runBtn.style.cursor = "pointer";
      runBtn.onclick = runAllTests;

      const logDiv = document.createElement("div");
      logDiv.id = "test-suite-log";
      logDiv.style.fontSize = "1rem";
      logDiv.style.maxHeight = "12em";
      logDiv.style.overflowY = "auto";
      logDiv.style.marginTop = "0.2rem";

      testPanel.appendChild(header);
      testPanel.appendChild(runBtn);
      testPanel.appendChild(logDiv);

      document.body.appendChild(testPanel);
    }
  </script>
</body>
</html>
