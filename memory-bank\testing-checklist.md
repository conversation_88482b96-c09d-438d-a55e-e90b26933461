# PseudoWrite Testing Checklist
*A living QA/reference document. Check off, add, or adjust as needed with every release or significant change.*

_Last updated: 2025-06-16_

---

## E2E Flow Coverage

### Onboarding & Help
- [x] Banner appears for new users on first load
  - PASSED: Banner shows on first load; content and layout correct.
- [x] Dismiss or close onboarding banner works
  - PASSED: Close (×) button removes banner; no layout shifts.
- [x] “?” button toggles help at any time
  - PASSED: Onboarding/help banner is hidden by default; toggled via '?' button; persists state and displays/collapses reliably.

### Story Bible CRUD
- [ ] Add character via sidebar “+”/modal
  - FAILED: Modal opens and handler/overlay debug log fires, but no character is created in sidebar nor persists after reload—UI state not updated.
- [ ] Add world element via sidebar “+”/modal
  - FAILED: Same as above; modal/debug overlay functional, but sidebar/item not updated or created.
- [ ] **After any add, delete, or multiple cycles, all sidebar “+” buttons remain functional and open modals—handler fires are visible in debug/status output.**
  - BLOCKED: Could not fully test, as no add/delete works; ATTACH/FIRE logs visible, but no sidebar update or persistence occurs.
- [x] Edit character details via modal and save
  - PASSED (seeded/example data only): Can edit and save fields in modal for existing (example) data; changes persist across reloads as expected.
- [x] Edit world element details via modal and save
  - PASSED (seeded/example data only): As above; editing and persistence work for seed data only.
- [ ] Delete character with confirmation
  - FAILED: Confirmation dialog/modal shows, but item is not removed from sidebar/state, and does not persist after reload.
- [ ] Delete world element with confirmation
  - FAILED: Same as above.
- [ ] Changes persist after reload (localStorage)
  - PARTIAL: Edits to seeded data persist. Adds/deletes do not happen, so cannot test creation/deletion persistence.

### Modal/Detail Editing
- [x] Open modal from sidebar for any item
  - PASSED for existing/example content.
- [x] Save/cancel modal changes reflect in UI
  - PASSED (seeded data only): Edits/cancels update view for example data.
- [x] Delete actions show proper warning dialog
  - PASSED: Dialog/modal is shown, but actual removal does not occur due to base bug above.
- [x] Modal closes on ESC and has keyboard focus loop
  - PASSED (basics): ESC closes modal, keyboard focus trapping works, but full ARIA/loop not fully verifiable here.

### AI Features
- [x] “Write” button invokes AI, provides result in editor
  - PASSED: Works as expected, with user/editor feedback.
- [x] “Rewrite” modifies selected text as expected
  - PASSED.
- [x] “Describe” provides context prose
  - PASSED.
- [x] Bad prompt/parse error shows clear UI error
  - PASSED (DEV harness/simulation): User-facing error logs appear in QA/dev overlay for simulated AI/bad input.

### Data Management
- [x] Export backup triggers valid file download
  - PASSED: Export works and file saves.
- [ ] Import backup populates app from file
  - PARTIAL: UI for import is present, but real file upload/import non-functional in this automation; simulated error handling appears in dev harness.
- [x] Clear/reset wipes state, shows correct empty-state UI
  - PASSED (via simulation/dev panel).

### Error Handling & Quota Management
- [x] Overfull storage warns user at 80% limit
  - PASSED (QA/dev panel): Simulated warning shown.
- [x] Importing invalid/corrupt file triggers correct error flow
  - PASSED (DEV harness/simulation): Error/alert displayed.
- [x] AI/network/unexpected error surfaces as actionable alerts
  - PASSED (DEV harness/simulation): User-facing error logs in toolbar or overlay.

### Accessibility
- [x] Modals focus-trap and close on ESC
  - PASSED: ESC closes, keyboard focus trapping present.
- [x] All main operations accessible via keyboard navigation (TAB, arrows)
  - PASSED (basics): Keyboard navigation works for modal/major flows.
- [x] Visible focus ring for interactive elements
  - PASSED: Focus ring visible for most elements.
- [x] ARIA labeling present on modals, main panels
  - PARTIAL: Properties and labels present by log/UI inspection; not every ARIA/loop verified live.

### Embedded DEV Harness (Edge Case)
- [x] DEV harness tests can be run manually in DEV mode
  - PASSED: Panel is visible/runnable in DEV/localhost.
- [x] Results display edge-case pass/fail in-panel
  - PASSED: All simulated key errors/fails logged.
- [ ] **Manual or DEV: Quickly cycle sidebar add/delete multiple times and confirm "+" handler is attached/fired after every state change. Debug overlay must show attach/fire for every test-run.**
  - BLOCKED: Handler debug overlays fire and attach, but CRUD bugs prevent full cycling test—sidebar does not update.
- [x] All critical edge-cases present: bad JSON, over-quota, import fail, empty-states
  - PASSED: Simulated in QA panel.

### Visual/Performance (Optional)
- [x] Key UIs load without layout shifts or major delay
  - PASSED: All major UIs stable on load.
- [x] Visual regression—major flows/screens unchanged across runs
  - PASSED: Visuals/layout stable across reloads.

---

> Updated 2025-06-16 after MCP server test run on v0.1. PASSED, FAILED, or PARTIAL results now reflect actual E2E and simulated coverage. Major block: add/delete for new entities—see Story Bible CRUD—remains broken. DEV harness simulates most error cases for ongoing QA between releases.
